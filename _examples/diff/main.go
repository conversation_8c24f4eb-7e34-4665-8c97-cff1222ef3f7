package main

import (
	"fmt"
	"log"
	"os"

	"github.com/go-git/go-git/v5"
)

// Example of how to get diff of working tree changes
func main() {
	// Open repository in current directory
	path := "."
	if len(os.Args) > 1 {
		path = os.Args[1]
	}

	repo, err := git.PlainOpen(path)
	if err != nil {
		log.Fatalf("Failed to open repository: %v", err)
	}

	// Get worktree
	worktree, err := repo.Worktree()
	if err != nil {
		log.Fatalf("Failed to get worktree: %v", err)
	}

	// Get status first to see what's changed
	status, err := worktree.Status()
	if err != nil {
		log.Fatalf("Failed to get status: %v", err)
	}

	if status.IsClean() {
		fmt.Println("Working tree is clean - no changes to show")
		return
	}

	// Show status
	fmt.Println("Working tree status:")
	fmt.Println(status)
	fmt.Println()

	// Get diff
	fmt.Println("Diff of working tree changes:")
	fmt.Println("=============================")

	patch, err := worktree.Diff()
	if err != nil {
		log.Fatalf("Failed to get diff: %v", err)
	}

	// Print the patch
	fmt.Print(patch.String())

	// Example with path filter
	if len(os.Args) > 2 {
		fmt.Println("\n\nFiltered diff (paths:", os.Args[2:], "):")
		fmt.Println("=====================================")

		filteredPatch, err := worktree.DiffWithOptions(&git.DiffOptions{
			Paths: os.Args[2:],
		})
		if err != nil {
			log.Fatalf("Failed to get filtered diff: %v", err)
		}

		fmt.Print(filteredPatch.String())
	}
}