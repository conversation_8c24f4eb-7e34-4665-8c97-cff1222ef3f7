package config

import (
	"bytes"

	. "gopkg.in/check.v1"
)

type EncoderSuite struct{}

var _ = Suite(&EncoderSuite{})

func (s *EncoderSuite) TestEncode(c *C) {
	for idx, fixture := range fixtures {
		buf := &bytes.Buffer{}
		e := NewEncoder(buf)
		err := e.Encode(fixture.Config)
		c.<PERSON>(err, Is<PERSON>il, Commentf("encoder error for fixture: %d", idx))
		c.<PERSON>(buf.String(), Equals, fixture.Text, Commentf("bad result for fixture: %d", idx))
	}
}
