package config

import (
	. "gopkg.in/check.v1"
)

type OptionSuite struct{}

var _ = Suite(&OptionSuite{})

func (s *OptionSuite) TestOptions_Has(c *C) {
	o := Options{
		&Option{"k", "v"},
		&Option{"ok", "v1"},
		&Option{"K", "v2"},
	}
	c.<PERSON>(o.<PERSON>("k"), Equals, true)
	c.<PERSON><PERSON><PERSON>(o.<PERSON>("K"), Equals, true)
	c.<PERSON>(o.<PERSON>("ok"), Equals, true)
	c.<PERSON>(o.<PERSON>("unexistant"), Equals, false)

	o = Options{}
	c.<PERSON><PERSON><PERSON>(o.<PERSON>("k"), Equals, false)
}

func (s *OptionSuite) TestOptions_GetAll(c *C) {
	o := Options{
		&Option{"k", "v"},
		&Option{"ok", "v1"},
		&Option{"K", "v2"},
	}
	c.<PERSON>(o.<PERSON>("k"), DeepEquals, []string{"v", "v2"})
	c.<PERSON><PERSON><PERSON>(o.<PERSON>("K"), DeepEquals, []string{"v", "v2"})
	c.<PERSON>(o.<PERSON>ll("ok"), DeepEquals, []string{"v1"})
	c.Assert(o.GetAll("unexistant"), DeepEquals, []string{})

	o = Options{}
	c.Assert(o.GetAll("k"), DeepEquals, []string{})
}

func (s *OptionSuite) TestOption_IsKey(c *C) {
	c.Assert((&Option{Key: "key"}).IsKey("key"), Equals, true)
	c.Assert((&Option{Key: "key"}).IsKey("KEY"), Equals, true)
	c.Assert((&Option{Key: "KEY"}).IsKey("key"), Equals, true)
	c.Assert((&Option{Key: "key"}).IsKey("other"), Equals, false)
	c.Assert((&Option{Key: "key"}).IsKey(""), Equals, false)
	c.Assert((&Option{Key: ""}).IsKey("key"), Equals, false)
}
