package plumbing

import (
	"testing"

	. "gopkg.in/check.v1"
)

type ReferenceSuite struct{}

var _ = Suite(&ReferenceSuite{})

const (
	ExampleReferenceName ReferenceName = "refs/heads/v4"
)

func (s *ReferenceSuite) TestReferenceTypeString(c *C) {
	c.<PERSON>sert(SymbolicReference.String(), Equals, "symbolic-reference")
}

func (s *ReferenceSuite) TestReferenceNameShort(c *C) {
	c.<PERSON>(ExampleReferenceName.Short(), Equals, "v4")
}

func (s *ReferenceSuite) TestReferenceNameWithSlash(c *C) {
	r := ReferenceName("refs/remotes/origin/feature/AllowSlashes")
	c.<PERSON><PERSON>(r.Short(), Equals, "origin/feature/AllowSlashes")
}

func (s *ReferenceSuite) TestReferenceNameNote(c *C) {
	r := ReferenceName("refs/notes/foo")
	c.<PERSON><PERSON>(r.<PERSON>(), Equals, "notes/foo")
}

func (s *ReferenceSuite) TestNewReferenceFromStrings(c *C) {
	r := NewReferenceFromStrings("refs/heads/v4", "6ecf0ef2c2dffb796033e5a02219af86ec6584e5")
	c.Assert(r.Type(), Equals, HashReference)
	c.Assert(r.Name(), Equals, ExampleReferenceName)
	c.Assert(r.Hash(), Equals, NewHash("6ecf0ef2c2dffb796033e5a02219af86ec6584e5"))

	r = NewReferenceFromStrings("HEAD", "ref: refs/heads/v4")
	c.Assert(r.Type(), Equals, SymbolicReference)
	c.Assert(r.Name(), Equals, HEAD)
	c.Assert(r.Target(), Equals, ExampleReferenceName)
}

func (s *ReferenceSuite) TestNewSymbolicReference(c *C) {
	r := NewSymbolicReference(HEAD, ExampleReferenceName)
	c.Assert(r.Type(), Equals, SymbolicReference)
	c.Assert(r.Name(), Equals, HEAD)
	c.Assert(r.Target(), Equals, ExampleReferenceName)
}

func (s *ReferenceSuite) TestNewHashReference(c *C) {
	r := NewHashReference(ExampleReferenceName, NewHash("6ecf0ef2c2dffb796033e5a02219af86ec6584e5"))
	c.Assert(r.Type(), Equals, HashReference)
	c.Assert(r.Name(), Equals, ExampleReferenceName)
	c.Assert(r.Hash(), Equals, NewHash("6ecf0ef2c2dffb796033e5a02219af86ec6584e5"))
}

func (s *ReferenceSuite) TestNewBranchReferenceName(c *C) {
	r := NewBranchReferenceName("foo")
	c.Assert(r.String(), Equals, "refs/heads/foo")
}

func (s *ReferenceSuite) TestNewNoteReferenceName(c *C) {
	r := NewNoteReferenceName("foo")
	c.Assert(r.String(), Equals, "refs/notes/foo")
}

func (s *ReferenceSuite) TestNewRemoteReferenceName(c *C) {
	r := NewRemoteReferenceName("bar", "foo")
	c.Assert(r.String(), Equals, "refs/remotes/bar/foo")
}

func (s *ReferenceSuite) TestNewRemoteHEADReferenceName(c *C) {
	r := NewRemoteHEADReferenceName("foo")
	c.Assert(r.String(), Equals, "refs/remotes/foo/HEAD")
}

func (s *ReferenceSuite) TestNewTagReferenceName(c *C) {
	r := NewTagReferenceName("foo")
	c.Assert(r.String(), Equals, "refs/tags/foo")
}

func (s *ReferenceSuite) TestIsBranch(c *C) {
	r := ExampleReferenceName
	c.Assert(r.IsBranch(), Equals, true)
}

func (s *ReferenceSuite) TestIsNote(c *C) {
	r := ReferenceName("refs/notes/foo")
	c.Assert(r.IsNote(), Equals, true)
}

func (s *ReferenceSuite) TestIsRemote(c *C) {
	r := ReferenceName("refs/remotes/origin/master")
	c.Assert(r.IsRemote(), Equals, true)
}

func (s *ReferenceSuite) TestIsTag(c *C) {
	r := ReferenceName("refs/tags/v3.1.")
	c.Assert(r.IsTag(), Equals, true)
}

func (s *ReferenceSuite) TestValidReferenceNames(c *C) {
	valid := []ReferenceName{
		"refs/heads/master",
		"refs/notes/commits",
		"refs/remotes/origin/master",
		"HEAD",
		"refs/tags/v3.1.1",
		"refs/pulls/1/head",
		"refs/pulls/1/merge",
		"refs/pulls/1/abc.123",
		"refs/pulls",
		"refs/-", // should this be allowed?
	}
	for _, v := range valid {
		c.Assert(v.Validate(), IsNil)
	}

	invalid := []ReferenceName{
		"refs",
		"refs/",
		"refs//",
		"refs/heads/\\",
		"refs/heads/\\foo",
		"refs/heads/\\foo/bar",
		"abc",
		"",
		"refs/heads/ ",
		"refs/heads/ /",
		"refs/heads/ /foo",
		"refs/heads/.",
		"refs/heads/..",
		"refs/heads/foo..",
		"refs/heads/foo.lock",
		"refs/heads/foo@{bar}",
		"refs/heads/foo[",
		"refs/heads/foo~",
		"refs/heads/foo^",
		"refs/heads/foo:",
		"refs/heads/foo?",
		"refs/heads/foo*",
		"refs/heads/foo[bar",
		"refs/heads/foo\t",
		"refs/heads/@",
		"refs/heads/@{bar}",
		"refs/heads/\n",
		"refs/heads/-foo",
		"refs/heads/foo..bar",
		"refs/heads/-",
		"refs/tags/-",
		"refs/tags/-foo",
	}

	for i, v := range invalid {
		comment := Commentf("invalid reference name case %d: %s", i, v)
		c.Assert(v.Validate(), NotNil, comment)
		c.Assert(v.Validate(), ErrorMatches, "invalid reference name", comment)
	}
}

func benchMarkReferenceString(r *Reference, b *testing.B) {
	for n := 0; n < b.N; n++ {
		_ = r.String()
	}
}

func BenchmarkReferenceStringSymbolic(b *testing.B) {
	benchMarkReferenceString(NewSymbolicReference("v3.1.1", "refs/tags/v3.1.1"), b)
}

func BenchmarkReferenceStringHash(b *testing.B) {
	benchMarkReferenceString(NewHashReference("v3.1.1", NewHash("6ecf0ef2c2dffb796033e5a02219af86ec6584e5")), b)
}

func BenchmarkReferenceStringInvalid(b *testing.B) {
	benchMarkReferenceString(&Reference{}, b)
}
