package plumbing

import . "gopkg.in/check.v1"

type ObjectSuite struct{}

var _ = Suite(&ObjectSuite{})

func (s *ObjectSuite) TestObjectTypeString(c *C) {
	c.<PERSON>sert(CommitObject.String(), Equals, "commit")
	c.<PERSON>(TreeObject.String(), Equals, "tree")
	c.<PERSON>(BlobObject.String(), Equals, "blob")
	c.<PERSON><PERSON><PERSON>(TagObject.String(), Equals, "tag")
	c.<PERSON><PERSON>(REFDeltaObject.String(), Equals, "ref-delta")
	c.<PERSON>sert(OFSDeltaObject.String(), Equals, "ofs-delta")
	c.<PERSON>sert(AnyObject.String(), Equals, "any")
	c.<PERSON>(ObjectType(42).String(), Equals, "unknown")
}

func (s *ObjectSuite) TestObjectTypeBytes(c *C) {
	c.<PERSON>sert(CommitObject.Bytes(), DeepEquals, []byte("commit"))
}

func (s *ObjectSuite) TestObjectTypeValid(c *C) {
	c.<PERSON>(CommitObject.Valid(), Equals, true)
	c.<PERSON>(ObjectType(42).Valid(), Equals, false)
}

func (s *ObjectSuite) TestParseObjectType(c *C) {
	for s, e := range map[string]ObjectType{
		"commit":    CommitObject,
		"tree":      TreeObject,
		"blob":      BlobObject,
		"tag":       TagObject,
		"ref-delta": REFDeltaObject,
		"ofs-delta": OFSDeltaObject,
	} {
		t, err := ParseObjectType(s)
		c.Assert(err, IsNil)
		c.Assert(e, Equals, t)
	}

	t, err := ParseObjectType("foo")
	c.Assert(err, Equals, ErrInvalidType)
	c.Assert(t, Equals, InvalidObject)
}
