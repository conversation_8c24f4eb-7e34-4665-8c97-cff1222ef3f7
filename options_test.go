package git

import (
	"os"

	"github.com/go-git/go-billy/v5/util"
	"github.com/go-git/go-git/v5/config"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/object"
	. "gopkg.in/check.v1"
)

type OptionsSuite struct {
	BaseSuite
}

var _ = Suite(&OptionsSuite{})

func (s *OptionsSuite) TestCommitOptionsParentsFromHEAD(c *C) {
	o := CommitOptions{Author: &object.Signature{}}
	err := o.Validate(s.Repository)
	c.<PERSON>sert(err, IsNil)
	c.<PERSON>(o.Parents, HasLen, 1)
}

func (s *OptionsSuite) TestResetOptionsCommitNotFound(c *C) {
	o := ResetOptions{Commit: plumbing.NewHash("ab1b15c6f6487b4db16f10d8ec69bb8bf91dcabd")}
	err := o.Validate(s.Repository)
	c.<PERSON>sert(err, NotNil)
}

func (s *OptionsSuite) TestCommitOptionsCommitter(c *C) {
	sig := &object.Signature{}

	o := CommitOptions{Author: sig}
	err := o.Validate(s.Repository)
	c.Assert(err, IsNil)

	c.Assert(o.Committer, Equals, o.Author)
}

func (s *OptionsSuite) TestCommitOptionsLoadGlobalConfigUser(c *C) {
	cfg := config.NewConfig()
	cfg.User.Name = "foo"
	cfg.User.Email = "<EMAIL>"

	clean := s.writeGlobalConfig(c, cfg)
	defer clean()

	o := CommitOptions{}
	err := o.Validate(s.Repository)
	c.Assert(err, IsNil)

	c.Assert(o.Author.Name, Equals, "foo")
	c.Assert(o.Author.Email, Equals, "<EMAIL>")
	c.Assert(o.Committer.Name, Equals, "foo")
	c.Assert(o.Committer.Email, Equals, "<EMAIL>")
}

func (s *OptionsSuite) TestCommitOptionsLoadGlobalCommitter(c *C) {
	cfg := config.NewConfig()
	cfg.User.Name = "foo"
	cfg.User.Email = "<EMAIL>"
	cfg.Committer.Name = "bar"
	cfg.Committer.Email = "<EMAIL>"

	clean := s.writeGlobalConfig(c, cfg)
	defer clean()

	o := CommitOptions{}
	err := o.Validate(s.Repository)
	c.Assert(err, IsNil)

	c.Assert(o.Author.Name, Equals, "foo")
	c.Assert(o.Author.Email, Equals, "<EMAIL>")
	c.Assert(o.Committer.Name, Equals, "bar")
	c.Assert(o.Committer.Email, Equals, "<EMAIL>")
}

func (s *OptionsSuite) TestCreateTagOptionsLoadGlobal(c *C) {
	cfg := config.NewConfig()
	cfg.User.Name = "foo"
	cfg.User.Email = "<EMAIL>"

	clean := s.writeGlobalConfig(c, cfg)
	defer clean()

	o := CreateTagOptions{
		Message: "foo",
	}

	err := o.Validate(s.Repository, plumbing.ZeroHash)
	c.Assert(err, IsNil)

	c.Assert(o.Tagger.Name, Equals, "foo")
	c.Assert(o.Tagger.Email, Equals, "<EMAIL>")
}

func (s *OptionsSuite) writeGlobalConfig(c *C, cfg *config.Config) func() {
	fs := s.TemporalFilesystem(c)

	tmp, err := util.TempDir(fs, "", "test-options")
	c.Assert(err, IsNil)

	err = fs.MkdirAll(fs.Join(tmp, "git"), 0777)
	c.Assert(err, IsNil)

	os.Setenv("XDG_CONFIG_HOME", fs.Join(fs.Root(), tmp))

	content, err := cfg.Marshal()
	c.Assert(err, IsNil)

	cfgFile := fs.Join(tmp, "git/config")
	err = util.WriteFile(fs, cfgFile, content, 0777)
	c.Assert(err, IsNil)

	return func() {
		os.Setenv("XDG_CONFIG_HOME", "")

	}
}
