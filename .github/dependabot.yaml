version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    commit-message:
      prefix: "build"

  - package-ecosystem: "gomod"
    directory: "/"
    schedule:
      interval: "daily"
    commit-message:
      prefix: "build"

  - package-ecosystem: "gomod"
    directory: "/cli/go-git"
    schedule:
      interval: "daily"
    commit-message:
      prefix: "build"
