on: [push, pull_request]
name: Git Compatibility
permissions:
  contents: read

jobs:
  test:
    strategy:
      fail-fast: false
      matrix:
        git: [[master, ubuntu-latest], [v2.11.0, ubuntu-latest]]

    runs-on: ${{ matrix.git[1] }}
    env:
        GIT_VERSION: ${{ matrix.git[0] }}
        GIT_DIST_PATH: .git-dist/${{ matrix.git[0] }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Go
      uses: actions/setup-go@v5
      with:
        go-version: 1.22.x

    - name: Install build dependencies
      run: sudo apt-get update && sudo apt-get install gettext libcurl4-openssl-dev

    - name: Git Build
      run: make build-git

    - name: Set Git config
      run: |
        export GIT_DIST_PATH=.git-dist/${{ matrix.git[0] }}
        export GIT_EXEC_PATH=${{ github.workspace }}/$GIT_DIST_PATH
        export PATH=$GIT_EXEC_PATH:$PATH
        git --exec-path
        git config --global user.email "<EMAIL>"
        git config --global user.name "GitHub Actions"
 
    - name: Test
      run: make test-coverage

    - name: Test SHA256
      run: make test-sha256

    - name: Build go-git with CGO disabled
      run: go build ./...
      env:
        CGO_ENABLED: 0
