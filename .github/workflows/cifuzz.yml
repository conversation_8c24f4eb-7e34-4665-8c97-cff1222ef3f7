name: CIFuzz
on: [pull_request]
permissions: {}
jobs:
 Fuzzing:
   runs-on: ubuntu-latest
   permissions:
     security-events: write
   steps:
   - name: Build Fuzzers
     id: build
     uses: google/oss-fuzz/infra/cifuzz/actions/build_fuzzers@master
     with:
       oss-fuzz-project-name: 'go-git'
       language: go
   - name: Run Fuzzers
     uses: google/oss-fuzz/infra/cifuzz/actions/run_fuzzers@master
     with:
       oss-fuzz-project-name: 'go-git'
       language: go
       fuzz-seconds: 300
       output-sarif: true
   - name: Upload Crash
     uses: actions/upload-artifact@v4
     if: failure() && steps.build.outcome == 'success'
     with:
       name: artifacts
       path: ./out/artifacts
   - name: Upload Sarif
     if: always() && steps.build.outcome == 'success'
     uses: github/codeql-action/upload-sarif@v3
     with:
      # Path to SARIF file relative to the root of the repository
      sarif_file: cifuzz-sarif/results.sarif
      checkout_path: cifuzz-sarif
