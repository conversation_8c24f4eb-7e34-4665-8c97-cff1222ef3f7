# go-git Security Policy

The purpose of this security policy is to outline `go-git`'s process
for reporting, handling and disclosing security sensitive information.

## Supported Versions

The project follows a version support policy where only the latest minor
release is actively supported. Therefore, only issues that impact the latest
minor release will be fixed. Users are encouraged to upgrade to the latest
minor/patch release to benefit from the most up-to-date features, bug fixes,
and security enhancements.​

The supported versions policy applies to both the `go-git` library and its
associated repositories within the `go-git` org.

## Reporting Security Issues

Please report any security vulnerabilities or potential weaknesses in `go-git`
<NAME_EMAIL>. Do not publicly disclose the
details of the vulnerability until a fix has been implemented and released.

During the process the project maintainers will investigate the report, so please
provide detailed information, including steps to reproduce, affected versions, and any mitigations if known.

The project maintainers will acknowledge the receipt of the report and work with
the reporter to validate and address the issue.

Please note that `go-git` does not have any bounty programs, and therefore do
not provide financial compensation for disclosures.

## Security Disclosure Process

The project maintainers will make every effort to promptly address security issues.

Once a security vulnerability is fixed, a security advisory will be published to notify users and provide appropriate mitigation measures.

All `go-git` advisories can be found at https://github.com/go-git/go-git/security/advisories.
